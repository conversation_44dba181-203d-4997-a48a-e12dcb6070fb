import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiGetLoyaltyPoints, apiGetPointsHistory } from '../services/LoyaltyPoints/LoyaltyPoints';

const LoyaltyContext = createContext();

export const useLoyalty = () => {
  const context = useContext(LoyaltyContext);
  if (!context) {
    throw new Error('useLoyalty must be used within a LoyaltyProvider');
  }
  return context;
};

export const LoyaltyProvider = ({ children }) => {
  const [loyaltyPoints, setLoyaltyPoints] = useState(0);
  const [pointsHistory, setPointsHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [username, setUsername] = useState(localStorage.getItem("user") || "Tài khoản");

  // Hàm lấy điểm thưởng từ API
  const fetchLoyaltyPoints = async (user = username) => {
    if (user !== "Tài khoản") {
      setLoading(true);
      try {
        const response = await apiGetLoyaltyPoints(user);
        if (response.data.status === 1) {
          const points = response.data.points || 0;
          setLoyaltyPoints(points);
          // Đồng bộ với localStorage
          localStorage.setItem(`loyaltyPoints_${user}`, points.toString());
        } else {
          // Nếu API trả về status khác 1, lấy từ localStorage
          const storedPoints = localStorage.getItem(`loyaltyPoints_${user}`);
          setLoyaltyPoints(storedPoints ? parseInt(storedPoints) : 0);
        }
      } catch (error) {
        console.error("Lỗi khi lấy điểm thưởng từ API:", error);
        // Fallback: lấy từ localStorage nếu API lỗi
        const storedPoints = localStorage.getItem(`loyaltyPoints_${user}`);
        const points = storedPoints ? parseInt(storedPoints) : 0;
        setLoyaltyPoints(points);
        console.log(`Fallback: Sử dụng điểm thưởng từ localStorage: ${points}`);
      } finally {
        setLoading(false);
      }
    } else {
      setLoyaltyPoints(0);
    }
  };

  // Hàm lấy lịch sử tích điểm từ API
  const fetchPointsHistory = async (user = username) => {
    if (user !== "Tài khoản") {
      try {
        const response = await apiGetPointsHistory(user);
        if (response.data.status === 1) {
          const history = response.data.history || [];
          setPointsHistory(history);
          // Đồng bộ với localStorage
          localStorage.setItem(`pointsHistory_${user}`, JSON.stringify(history));
        } else {
          // Nếu API trả về status khác 1, lấy từ localStorage
          const storedHistory = JSON.parse(localStorage.getItem(`pointsHistory_${user}`) || '[]');
          setPointsHistory(storedHistory);
        }
      } catch (error) {
        console.error("Lỗi khi lấy lịch sử điểm thưởng từ API:", error);
        // Fallback: lấy từ localStorage nếu API lỗi
        const storedHistory = JSON.parse(localStorage.getItem(`pointsHistory_${user}`) || '[]');
        setPointsHistory(storedHistory);
        console.log(`Fallback: Sử dụng lịch sử từ localStorage: ${storedHistory.length} items`);
      }
    } else {
      setPointsHistory([]);
    }
  };

  // Hàm refresh toàn bộ dữ liệu điểm thưởng
  const refreshLoyaltyData = async (user = username) => {
    await Promise.all([
      fetchLoyaltyPoints(user),
      fetchPointsHistory(user)
    ]);
  };

  // Hàm cập nhật điểm thưởng local (để đồng bộ ngay lập tức)
  const updateLocalPoints = (newPoints) => {
    setLoyaltyPoints(newPoints);
    localStorage.setItem(`loyaltyPoints_${username}`, newPoints.toString());
  };

  // Hàm thêm điểm thưởng local
  const addLocalPoints = (pointsToAdd) => {
    const newPoints = loyaltyPoints + pointsToAdd;
    updateLocalPoints(newPoints);
  };

  // Hàm thêm lịch sử tích điểm local
  const addLocalHistory = (historyItem) => {
    const newHistory = [historyItem, ...pointsHistory];
    setPointsHistory(newHistory);
    localStorage.setItem(`pointsHistory_${username}`, JSON.stringify(newHistory));
  };

  // Effect để lấy dữ liệu khi username thay đổi
  useEffect(() => {
    const currentUser = localStorage.getItem("user") || "Tài khoản";
    setUsername(currentUser);
    if (currentUser !== "Tài khoản") {
      refreshLoyaltyData(currentUser);
    } else {
      setLoyaltyPoints(0);
      setPointsHistory([]);
    }
  }, []);

  // Effect để theo dõi thay đổi localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const currentUser = localStorage.getItem("user") || "Tài khoản";
      if (currentUser !== username) {
        setUsername(currentUser);
        if (currentUser !== "Tài khoản") {
          refreshLoyaltyData(currentUser);
        } else {
          setLoyaltyPoints(0);
          setPointsHistory([]);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [username]);

  const value = {
    loyaltyPoints,
    pointsHistory,
    loading,
    username,
    fetchLoyaltyPoints,
    fetchPointsHistory,
    refreshLoyaltyData,
    updateLocalPoints,
    addLocalPoints,
    addLocalHistory
  };

  return (
    <LoyaltyContext.Provider value={value}>
      {children}
    </LoyaltyContext.Provider>
  );
};
