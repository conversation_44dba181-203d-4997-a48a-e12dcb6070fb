import http from "../http";

// L<PERSON>y điểm thưởng hiện tại của user
export const apiGetLoyaltyPoints = (username) => {
    return http.get(`/loyaltypoints.php?username=${username}`);
};

// Cập nhật điểm thưởng khi có đơn hàng mới
export const apiUpdateLoyaltyPoints = async (data) => {
    try {
        const response = await http.post('/loyaltypoints.php', data);
        return response.data;
    } catch (error) {
        console.error("Error while updating loyalty points:", error);
        throw error;
    }
};

// L<PERSON>y lịch sử tích điểm
export const apiGetPointsHistory = (username) => {
    return http.get(`/pointshistory.php?username=${username}`);
};

// Sử dụng điểm thưởng (đổi quà, giảm giá)
export const apiRedeemPoints = async (data) => {
    try {
        const response = await http.post('/redeempoints.php', data);
        return response.data;
    } catch (error) {
        console.error("Error while redeeming points:", error);
        throw error;
    }
};
