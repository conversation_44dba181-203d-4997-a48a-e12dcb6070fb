// API mô phỏng cho chức năng chat AI
// Đặt file này trong thư mục public để có thể truy cập từ frontend

// Biến lưu trữ dữ liệu mô phỏng
let mockData = {
  conversations: [],
  lastId: 0
};

// Hàm xử lý yêu cầu AI
function handleAIRequest(request) {
  const { message, conversationHistory } = request;
  
  // Danh sách các câu trả lời mẫu dựa trên từ khóa
  const responses = {
    "guitar": "Dựa trên kinh nghiệm của tôi, nếu bạn mới bắt đầu học guitar, tôi khuyên bạn nên chọn guitar acoustic hoặc classic. Guitar classic có dây nylon mềm hơn, dễ bấm hơn cho người mới.",
    "piano": "Piano là một nhạc cụ tuyệt vời để học! Chúng tôi có nhiều loại piano điện và piano cơ. Nếu bạn mới bắt đầu hoặc sống trong căn hộ, piano điện là lựa chọn tốt.",
    "trống": "Trống là một nhạc cụ thú vị nhưng cần không gian và có thể gây ồn. Chúng tôi có cả bộ trống acoustic truyền thống và trống điện tử.",
    "khuyến mãi": "Hiện tại cửa hàng chúng tôi đang có hai chương trình khuyến mãi đặc biệt: Giảm giá 20% cho tất cả sản phẩm vào thứ 6 hàng tuần và giảm giá 5% cho tất cả sản phẩm vào lúc 9h sáng mỗi ngày.",
    "giá": "Giá cả các sản phẩm tại cửa hàng chúng tôi rất đa dạng, phù hợp với nhiều đối tượng khách hàng. Guitar từ 900.000đ đến hơn 5 triệu đồng, Piano từ 5 triệu đến 20 triệu đồng."
  };
  
  // Tìm phản hồi phù hợp dựa trên từ khóa
  let response = "Tôi hiểu câu hỏi của bạn về nhạc cụ. Để tôi có thể giúp bạn tốt hơn, bạn có thể cung cấp thêm thông tin về loại nhạc cụ bạn quan tâm?";
  
  const lowerCaseMessage = message.toLowerCase();
  for (const keyword in responses) {
    if (lowerCaseMessage.includes(keyword)) {
      response = responses[keyword];
      break;
    }
  }
  
  return {
    status: 1,
    response: response
  };
}

// Hàm xử lý yêu cầu chuyển tiếp tin nhắn đến admin
function handleAdminForward(request) {
  const { message, userInfo } = request;
  
  // Tìm hoặc tạo cuộc hội thoại mới
  let conversation = mockData.conversations.find(conv => 
    conv.customer && conv.customer.email === userInfo.email
  );
  
  if (!conversation) {
    mockData.lastId++;
    conversation = {
      id: mockData.lastId,
      customer: userInfo,
      messages: [],
      unreadCount: 0,
      lastMessageTime: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      lastMessageDate: new Date().toISOString().split('T')[0]
    };
    mockData.conversations.push(conversation);
  }
  
  // Thêm tin nhắn mới
  conversation.messages.push({
    id: conversation.messages.length + 1,
    text: message,
    sender: 'customer',
    time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    date: new Date().toISOString().split('T')[0],
    isRead: false,
    isDisplayedToUser: true
  });
  
  conversation.unreadCount++;
  
  return {
    status: 1,
    message: "Tin nhắn đã được chuyển tiếp đến admin"
  };
}

// Hàm xử lý yêu cầu đồng bộ tin nhắn
function handleSyncMessages(request) {
  // Trong môi trường thực tế, bạn sẽ đồng bộ dữ liệu với backend
  // Ở đây chúng ta chỉ trả về dữ liệu mô phỏng
  return {
    status: 1,
    message: "Đồng bộ thành công",
    conversations: mockData.conversations
  };
}

// Mô phỏng API endpoint
window.mockAPI = {
  'ai-assistant.php': handleAIRequest,
  'admin-messages.php': handleAdminForward,
  'sync-messages.php': handleSyncMessages
};

console.log('API mô phỏng đã được khởi tạo');
