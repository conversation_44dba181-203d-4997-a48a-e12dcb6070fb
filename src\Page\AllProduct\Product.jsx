import React, { useEffect, useState, useRef } from "react";
import { getListBooks } from "../../services/Listbook/Listbook";
import { useNavigate } from "react-router-dom";

const Product = () => {
  const [books, setBooks] = useState([]);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(10);
  const [isFriday, setIsFriday] = useState(false);
  const [isNineAM, setIsNineAM] = useState(false);

  // Chatbox states
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      text: "Xin chào! Tôi là trợ lý ảo của Music Store. Tôi có thể giúp gì cho bạn về thông tin cửa hàng, sả<PERSON> phẩm, địa chỉ hoặc khuyến mãi?",
      sender: "ai"
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        const response = await getListBooks();
        console.log(response); // Kiểm tra phản hồi từ API

        if (response.data.status === 1) {
          setBooks(response.data.books);
          console.log("Data: ", response.data.books);
        } else {
          setError(response.data.msg);
        }
      } catch (error) {
        setError("Không thể tải danh sách sách. Vui lòng thử lại.");
        console.error("Lỗi khi gọi API:", error.message);
      }
    };

    fetchBooks();

    // Check if today is Thursday
    const today = new Date();
    setIsFriday(today.getDay() === 4); // 0 is Sunday, 4 is Thursday

    // Check if current time is around 9 AM (between 8:30 AM and 9:30 AM)
    const currentHour = today.getHours();
    const currentMinute = today.getMinutes();
    setIsNineAM(
      (currentHour === 8 && currentMinute >= 30) ||
      (currentHour === 9 && currentMinute <= 30) ||
      (currentHour === 9 && currentMinute === 0)
    );

    // Set up interval to check time every minute
    const timeInterval = setInterval(() => {
      const now = new Date();
      const hour = now.getHours();
      const minute = now.getMinutes();
      setIsNineAM(
        (hour === 8 && minute >= 30) ||
        (hour === 9 && minute <= 30) ||
        (hour === 9 && minute === 0)
      );
    }, 60000); // Check every minute

    return () => clearInterval(timeInterval);
  }, []);

      const handleDetail = (item) => {
        navigate(`/chi-tiet-san-pham/${item.id}`);
      };

  // Get current products
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = books.slice(indexOfFirstProduct, indexOfLastProduct);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Calculate total pages
  const totalPages = Math.ceil(books.length / productsPerPage);

  // Scroll to bottom of chat messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (isChatOpen) {
      scrollToBottom();
    }
  }, [messages, isChatOpen]);

  // Handle sending a new message
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim() === "") return;

    // Add user message
    const userMessage = { text: newMessage, sender: "user" };
    setMessages([...messages, userMessage]);
    setNewMessage("");

    // Generate AI response based on user query
    setTimeout(() => {
      const aiResponse = generateAIResponse(newMessage);
      setMessages(prev => [...prev, { text: aiResponse, sender: "ai" }]);
    }, 500);
  };

  // Generate AI response based on user query
  const generateAIResponse = (query) => {
    const lowerQuery = query.toLowerCase();

    // Information about store locations
    if (lowerQuery.includes("địa chỉ") || lowerQuery.includes("cửa hàng") || lowerQuery.includes("showroom")) {
      return "Music Store có 2 showroom: \n1. 218 Lĩnh Nam, Hoàng Mai, Hà Nội \n2. Khu Đô Thị An Huy, Vũ Ninh, Bắc Ninh \nGiờ mở cửa: 8:30 - 20:00 tất cả các ngày trong tuần.";
    }

    // Information about promotions
    else if (lowerQuery.includes("khuyến mãi") || lowerQuery.includes("giảm giá") || lowerQuery.includes("ưu đãi")) {
      return "Music Store đang có chương trình khuyến mãi giảm giá 20% vào mỗi thứ Năm và giảm 5% vào lúc 9 giờ sáng mỗi ngày. Ngoài ra, chúng tôi còn có chương trình tích điểm thành viên để tri ân khách hàng.";
    }

    // Information about products
    else if (lowerQuery.includes("sản phẩm") || lowerQuery.includes("nhạc cụ") || lowerQuery.includes("đàn")) {
      return "Music Store chuyên kinh doanh các loại nhạc cụ như đàn guitar, piano, organ, trống, sáo, ukulele và các phụ kiện âm nhạc. Giá đàn guitar dao động từ 900.000đ đến hơn 5 triệu đồng tùy loại.";
    }

    // Information about contact
    else if (lowerQuery.includes("liên hệ") || lowerQuery.includes("số điện thoại") || lowerQuery.includes("hotline")) {
      return "Bạn có thể liên hệ với Music Store qua số điện thoại: 038886666 để được hỗ trợ.";
    }

    // Information about payment methods
    else if (lowerQuery.includes("thanh toán") || lowerQuery.includes("chuyển khoản") || lowerQuery.includes("qr")) {
      return "Music Store hỗ trợ thanh toán bằng tiền mặt, chuyển khoản ngân hàng (quét mã QR 'Music Store'), và các phương thức thanh toán trực tuyến phổ biến.";
    }

    // Default response
    else {
      return "Cảm ơn bạn đã liên hệ. Nếu bạn cần thông tin về địa chỉ cửa hàng, khuyến mãi, sản phẩm hoặc cách thức thanh toán, vui lòng cho tôi biết. Hoặc bạn có thể gọi số 038886666 để được hỗ trợ trực tiếp.";
    }
  };

  // Toggle chat window
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);

  };

  return (
    <>
      <div className="">
        <div className="w-screen">
          <div className="mx-14 my-8">
            <div>
              <div className="mt-2 grid grid-cols-5 gap-2">
                <div className="col-span-5 bg-white shadow-lg rounded">
                  <div className="ml-4 py-4 flex items-center justify-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={2.5}
                      stroke="currentColor"
                      className="size-6 text-red-700"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                      />
                    </svg>

                    <p className="ml-2 uppercase font-bold text-black">
                      tất cả sản phẩm
                    </p>
                  </div>
                  <div className="border border-solid border-gray-100 mb-2"></div>
                </div>
                {currentProducts.map((item) => (
                  <div
                    onClick={() => handleDetail(item)}
                    key={item.id}
                    className="col-span-1 cursor-pointer bg-white shadow-lg rounded transform transition-transform duration-300 hover:translate-y-1"
                  >
                    <div className="">
                      <img
                        src={`/image/${item.image}`}
                        alt="Sản phẩm"
                        className="h-[160px] ml-4"
                      />
                    </div>
                    <div className="mt-4 text-xs cursor-pointer hover:text-gray-600 font-normal text-gray-700">
                      <p className="h-[40px] leading-[20px] mx-2 text-left line-clamp-2">
                        {item.title}
                      </p>
                      <div className="flex mt-3 items-center justify-start">
                        <p className="text-gray-600 font-medium text-xs mx-2 line-through">
                          {" "}
                          {Number(item.priceold).toLocaleString("vi-VN")} đ
                        </p>
                        <p className="bg-red-500 ml-2 text-sm text-white font-semibold shadow-lg rounded px-2 py-1">
                          {" "}
                          - {item.sale}
                        </p>
                      </div>

                      {/* Flash Sale Badge */}
                      {(isFriday || isNineAM) && (
                        <div className="mx-2 mt-2">
                          <div className="bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            {isFriday ? "Flash Sale Thứ 5 - 20%" : "Flash Sale 9h - 5%"}
                          </div>
                        </div>
                      )}

                      <div className="flex pb-3 mt-2 items-center justify-between mx-2">
                        <p className="text-red-500 text-sm font-semibold">
                          {Number(item.price).toLocaleString("vi-VN")} đ
                        </p>
                        <p className="text-gray-600 font-normal text-xs">
                          {" "}
                          đã bán : {item.sold}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <nav className="flex items-center">
                    <button
                      onClick={() => currentPage > 1 && paginate(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`px-3 py-1 mx-1 rounded ${
                        currentPage === 1
                          ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                          : "bg-white text-red-700 hover:bg-red-100 border border-red-300"
                      }`}
                    >
                      &laquo;
                    </button>

                    {[...Array(totalPages).keys()].map(number => (
                      <button
                        key={number + 1}
                        onClick={() => paginate(number + 1)}
                        className={`px-3 py-1 mx-1 rounded ${
                          currentPage === number + 1
                            ? "bg-red-600 text-white"
                            : "bg-white text-red-700 hover:bg-red-100 border border-red-300"
                        }`}
                      >
                        {number + 1}
                      </button>
                    ))}

                    <button
                      onClick={() => currentPage < totalPages && paginate(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`px-3 py-1 mx-1 rounded ${
                        currentPage === totalPages
                          ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                          : "bg-white text-red-700 hover:bg-red-100 border border-red-300"
                      }`}
                    >
                      &raquo;
                    </button>
                  </nav>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Chat Button */}
      <div
        className="fixed bottom-6 right-6 bg-red-600 text-white p-4 rounded-full shadow-lg cursor-pointer hover:bg-red-700 transition-all z-50"
        onClick={toggleChat}
      >
        {isChatOpen ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        )}
      </div>

      {/* Chat Window */}
      {isChatOpen && (
        <div className="fixed bottom-20 right-6 w-80 md:w-96 bg-white rounded-lg shadow-xl z-50 overflow-hidden flex flex-col" style={{ height: "500px", maxHeight: "70vh" }}>
          {/* Chat Header */}
          <div className="bg-red-600 text-white px-4 py-3 flex justify-between items-center">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
              <h3 className="font-medium">Trợ lý Music Store</h3>
            </div>
            <button onClick={toggleChat} className="text-white hover:text-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`mb-3 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}
              >
                <div
                  className={`inline-block px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-red-600 text-white'
                      : 'bg-white text-gray-800 border border-gray-200'
                  } max-w-[80%]`}
                >
                  <p className="whitespace-pre-line text-sm">{message.text}</p>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input */}
          <form onSubmit={handleSendMessage} className="border-t border-gray-200 p-3 flex">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Nhập tin nhắn..."
              className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="bg-red-600 text-white px-4 py-2 rounded-r-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </form>
        </div>
      )}
    </>
  );
};

export default Product;
