# Hệ Thống Điểm Thưởng (Loyalty Points System)

## Tổng quan
Hệ thống điểm thưởng đã được cập nhật để hoạt động động và tự động cập nhật khi người dùng thực hiện mua hàng.

## Các thay đổi chính

### 1. Service API mới (`src/services/LoyaltyPoints/LoyaltyPoints.js`)
- `apiGetLoyaltyPoints(username)`: L<PERSON>y điểm thưởng hiện tại
- `apiUpdateLoyaltyPoints(data)`: Cập nhật điểm thưởng khi có đơn hàng mới
- `apiGetPointsHistory(username)`: <PERSON><PERSON><PERSON> lịch sử tích điểm
- `apiRedeemPoints(data)`: Sử dụng điểm thưởng

### 2. Context quản lý state toàn cục (`src/contexts/LoyaltyContext.js`)
- Quản lý state điểm thưởng cho toàn ứng dụng
- Tự động đồng bộ khi user thay đổi
- Cung cấp các function để cập nhật điểm thưởng

### 3. Header component được cập nhật
- Sử dụng LoyaltyContext thay vì localStorage
- Có nút "Làm mới" để cập nhật điểm thưởng từ server
- Hiển thị điểm thưởng real-time

### 4. Pay component được cập nhật
- Tự động tính và cập nhật điểm thưởng khi thanh toán thành công
- Quy tắc: Mỗi 100,000đ = 1 điểm thưởng
- Hiển thị thông báo khi tích điểm thành công

## Cách hoạt động

### Tích điểm tự động
1. Khi user thanh toán đơn hàng thành công
2. Hệ thống tự động tính điểm: `Math.floor(orderTotal / 100000)`
3. Gọi API để lưu điểm thưởng vào database
4. Cập nhật state local ngay lập tức
5. Refresh dữ liệu từ server sau 1 giây để đồng bộ

### Hiển thị điểm thưởng
- Header luôn hiển thị điểm thưởng hiện tại
- Popup chi tiết với lịch sử tích điểm
- Quy đổi điểm thưởng thành tiền mặt
- Nút "Làm mới" để cập nhật từ server

## API Backend cần implement

### 1. `/loyaltypoints.php`
**GET**: Lấy điểm thưởng hiện tại
```php
// Input: ?username=abc
// Output: {"status": 1, "points": 150}
```

**POST**: Cập nhật điểm thưởng
```php
// Input: {
//   "username": "abc",
//   "points": 5,
//   "orderAmount": 500000,
//   "orderDate": "2024-01-15",
//   "description": "Tích điểm từ đơn hàng 500,000đ"
// }
// Output: {"status": 1, "message": "Updated successfully"}
```

### 2. `/pointshistory.php`
**GET**: Lấy lịch sử tích điểm
```php
// Input: ?username=abc
// Output: {
//   "status": 1,
//   "history": [
//     {"id": 1, "date": "15/06/2023", "amount": 1200000, "points": 12},
//     {"id": 2, "date": "22/07/2023", "amount": 850000, "points": 8}
//   ]
// }
```

### 3. `/redeempoints.php`
**POST**: Sử dụng điểm thưởng
```php
// Input: {
//   "username": "abc",
//   "pointsUsed": 100,
//   "redeemType": "cash", // hoặc "voucher", "shipping"
//   "redeemValue": 10000
// }
// Output: {"status": 1, "message": "Redeemed successfully"}
```

## Database Schema đề xuất

### Bảng `loyalty_points`
```sql
CREATE TABLE loyalty_points (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    total_points INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_username (username)
);
```

### Bảng `points_history`
```sql
CREATE TABLE points_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    points_earned INT NOT NULL,
    order_amount DECIMAL(10,2),
    order_date DATE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_order_date (order_date)
);
```

### Bảng `points_redemption`
```sql
CREATE TABLE points_redemption (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    points_used INT NOT NULL,
    redeem_type VARCHAR(50), -- 'cash', 'voucher', 'shipping'
    redeem_value DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_username (username)
);
```

## Testing

### Test tích điểm
1. Đăng nhập với user
2. Thêm sản phẩm vào giỏ hàng
3. Thanh toán đơn hàng
4. Kiểm tra điểm thưởng có tăng không
5. Kiểm tra lịch sử tích điểm

### Test hiển thị
1. Click vào "Điểm thưởng" ở header
2. Kiểm tra popup hiển thị đúng thông tin
3. Click "Làm mới" để test refresh
4. Kiểm tra điểm thưởng hiển thị ở menu dropdown

## Lưu ý
- Hệ thống có fallback về localStorage nếu API lỗi
- Điểm thưởng được cập nhật real-time khi thanh toán
- Context được wrap ở App.js để toàn ứng dụng có thể sử dụng
- Cần implement các API backend tương ứng để hệ thống hoạt động hoàn chỉnh
