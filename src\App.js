import React, { useState, useEffect } from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import Login from "./Login/login";
import Register from "./Login/register";
import DefaultLayout from "./components/defaultLayout/DefaultLayout";
import Home from "./Page/Home/Home";
import Detail from "./Page/DetailProduct/Detail";
import CartBook from "./Page/Cart/CartBook";
import Product from "./Page/AllProduct/Product";
import Notify from "./Page/Notifycation/Notify";
import LoginNew from "./Login/loginNew";
import Infor from "./Page/Infor/Infor";
import ChangPass from "./Page/Infor/ChangPass";
import Address from "./Page/Infor/Address";
import Pay from "./Page/Cart/Pay";
import ChangAddress from "./Page/Infor/ChangAdd";
import Contact from "./Page/Contact/Contact";
import MyOder from "./Page/MyOder/MyOder";
import SearchBook from "./Page/SearchName/Search";
import Kinhte from "./Page/Type/Piano";
import Vanhoc from "./Page/Type/trong";
import Thieunhi from "./Page/Type/Children";
import KiNangSong from "./Page/Type/sao";
import ForgotPassword from "./Login/ForgotPass";
import Micro from "./Page/Type/Micro";
import Organ from "./Page/Type/Organ";

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken");
    setIsAuthenticated(!!accessToken);
  }, []);

  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem("accessToken");
  };

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<Login onLogin={handleLogin} />} />
        <Route path="/loginnew" element={<LoginNew />} />
        <Route path="/register" element={<Register />} />
        <Route path="/quen-mat-khau" element={<ForgotPassword />} />
        <Route path="/" element={<DefaultLayout children={<Home />} />} />
        <Route path="/chi-tiet-san-pham/:id" element={<DefaultLayout children={<Detail />} />} />
        <Route path="/gio-hang-san-pham" element={<DefaultLayout children={<CartBook />} />} />
        <Route path="/tat-ca-san-pham" element={<DefaultLayout children={<Product />} />} />
        <Route path="/thong-bao" element={<DefaultLayout children={<Notify />} />} />
        <Route path="/ho-so-ca-nhan" element={<DefaultLayout children={<Infor />} />} />
        <Route path="/doi-mat-khau" element={<DefaultLayout children={<ChangPass />} />} />
        <Route path="/dia-chi" element={<DefaultLayout children={<Address />} />} />
        <Route path="/thanh-toan" element={<DefaultLayout children={<Pay />} />} />
        <Route path="/thay-doi-dia-chi" element={<DefaultLayout children={<ChangAddress />} />} />
        <Route path="/lien-he" element={<DefaultLayout children={<Contact />} />} />
        <Route path="/don-hang-cua-toi" element={<DefaultLayout children={<MyOder />} />} />
        <Route path="/tim-kiem/:title" element={<DefaultLayout children={<SearchBook/>} />} />
        <Route path="/danh-muc-piano" element={<DefaultLayout children={<Kinhte />} />} />
        <Route path="/danh-muc-trong" element={<DefaultLayout children={<Vanhoc />} />} />
        <Route path="/danh-muc-guitar" element={<DefaultLayout children={<Thieunhi />} />} />
        <Route path="/danh-muc-sao" element={<DefaultLayout children={<KiNangSong />} />} />
        <Route path="/danh-muc-micro" element={<DefaultLayout children={<Micro />} />} />
        <Route path="/danh-muc-organ" element={<DefaultLayout children={<Organ />} />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;