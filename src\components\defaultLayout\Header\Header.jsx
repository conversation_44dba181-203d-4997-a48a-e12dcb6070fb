import React, { useEffect, useRef, useState } from "react";
import logo from "../../../image/book2.jpg";
import avatar from "../../../image/avatar.jpg";
import { Link, useNavigate } from "react-router-dom";
import { apigetCartlBook } from "../../../services/Cartbook/Cartbook";
import { useLoyalty } from "../../../contexts/LoyaltyContext";
import anhnen from "../../../image/anhnen.jpg";
const Header = () => {
  const [isShow, setIsShow] = useState(false);
  const [isShowLoyalty, setIsShowLoyalty] = useState(false);
  const [isShowRedeemModal, setIsShowRedeemModal] = useState(false);
  const [isShowHistoryModal, setIsShowHistoryModal] = useState(false);
  const menuRef = useRef(null);
  const loyaltyRef = useRef(null);
  const navigate = useNavigate();
  const [Cart, setCart] = useState([]);
  const [username, setUsername] = useState(
    localStorage.getItem("user") || "Tài khoản"
  );

  // Sử dụng LoyaltyContext
  const {
    loyaltyPoints,
    pointsHistory: purchaseHistory,
    refreshLoyaltyData,
    addLocalPoints,
    addLocalHistory,
    updateLocalPoints,
    loading: loyaltyLoading
  } = useLoyalty();

  // State cho đổi điểm thưởng
  const [selectedRedeemOption, setSelectedRedeemOption] = useState('');
  const [redeemHistory, setRedeemHistory] = useState([]);

  const handleHome = () => navigate("/gio-hang-san-pham");
  const handleNotifi = () => navigate("/thong-bao");

  const handleLogout = () => {
    localStorage.removeItem("user");
    localStorage.removeItem("idcus");
    navigate("/login");
  };

  // Hàm xử lý đổi điểm thưởng
  const handleRedeemPoints = (option) => {
    const redeemOptions = {
      '50000': { points: 500, value: 50000, type: 'cash', description: '50.000đ tiền mặt' },
      '100000': { points: 1000, value: 100000, type: 'cash', description: '100.000đ tiền mặt' },
      '200000': { points: 2000, value: 200000, type: 'cash', description: '200.000đ tiền mặt' },
      'shipping': { points: 5000, value: 0, type: 'shipping', description: 'Miễn phí vận chuyển 3 tháng' }
    };

    const selectedOption = redeemOptions[option];
    if (!selectedOption) return;

    if (loyaltyPoints < selectedOption.points) {
      alert(`Bạn cần ít nhất ${selectedOption.points} điểm để đổi ${selectedOption.description}`);
      return;
    }

    const confirmRedeem = window.confirm(
      `Bạn có chắc muốn đổi ${selectedOption.points} điểm lấy ${selectedOption.description}?`
    );

    if (confirmRedeem) {
      // Trừ điểm thưởng
      const newPoints = loyaltyPoints - selectedOption.points;
      updateLocalPoints(newPoints);

      // Thêm vào lịch sử đổi điểm
      const newRedeemItem = {
        id: Date.now(),
        date: new Date().toLocaleDateString('vi-VN'),
        pointsUsed: selectedOption.points,
        redeemType: selectedOption.type,
        redeemValue: selectedOption.value,
        description: selectedOption.description
      };

      const existingRedeemHistory = JSON.parse(localStorage.getItem(`redeemHistory_${username}`) || '[]');
      existingRedeemHistory.unshift(newRedeemItem);
      localStorage.setItem(`redeemHistory_${username}`, JSON.stringify(existingRedeemHistory));
      setRedeemHistory(existingRedeemHistory);

      alert(`✅ Đã đổi thành công! Bạn nhận được ${selectedOption.description}`);
      setIsShowRedeemModal(false);
    }
  };

  // Load lịch sử đổi điểm khi component mount
  useEffect(() => {
    if (username !== "Tài khoản") {
      const existingRedeemHistory = JSON.parse(localStorage.getItem(`redeemHistory_${username}`) || '[]');
      setRedeemHistory(existingRedeemHistory);
    }
  }, [username]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsShow(false);
      }
      if (loyaltyRef.current && !loyaltyRef.current.contains(event.target)) {
        setIsShowLoyalty(false);
      }
    };

    if (isShow || isShowLoyalty) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isShow, isShowLoyalty]);

  const fetchBooks = async () => {
    try {
      const response = await apigetCartlBook(username);
      if (response.data.status === 1) {
        const booksWithQuantity = response.data.books.map((book) => ({
          ...book,
          quantity: 1,
        }));
        setCart(booksWithQuantity);
      } else {
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchBooks();
  }, [username]);

  const [title , setTitle] = useState("");
  const handleSearch = () => {
    navigate(`/tim-kiem/${title}`);
  };

  return (
    <>
      <div className=" z-30 containe fixed left-0 right-0">
        <div className="">
          <div className="flex items-start justify-between w-full h-[60px]  bg-white shadow-lg">
            <div className=" flex items-start justify-start ">
              <Link to="/">
                <img
                  className="pl-4 h-[56px] mt-1 w-auto object-cover"
                  src={anhnen}
                  alt="Logo Bán Sách"
                />
              </Link>
              <p className=" uppercase mt-6 font-bold text-xl ml-1 text-red-500">
                {/* Music Store */}
              </p>
            </div>

            <div className=" flex-1 mt-2 ml-[10%]">
              <div className="flex items-center justify-around py-2 border border-solid rounded-lg border-gray-400">
                <input
                  className="ml-2 flex-1 text-sm px-2 outline-none border-none "
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Tìm kiếm sản phẩm ..."
                />
                <div onClick={handleSearch} className=" bg-red-700  px-4 mr-2 rounded py-1 hover:bg-red-600 cursor-pointer ">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2.5}
                    stroke="currentColor"
                    className="size-4 font-bold text-white"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <div className="flex flex-1 pr-4 fle font-semibold items-center justify-end text-gray-500 mr-4 mt-3">
              <a
              href="/thong-bao"
                className=" cursor-pointer xs:hidden lg:block"
              >

                <div className="flex mr-4 text-sm ">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2.5}
                    stroke="currentColor"
                    className="size-5 mr-1"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5"
                    />
                  </svg>
                  Thông báo
                </div>
              </a>

              {/* Điểm thưởng */}
              {username !== "Tài khoản" && (
                <div className="relative xs:hidden lg:block">
                  <div
                    onClick={() => setIsShowLoyalty(!isShowLoyalty)}
                    className="flex mr-4 cursor-pointer text-sm"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={2.5}
                      stroke="currentColor"
                      className="size-5 mr-1 text-yellow-600"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"
                      />
                    </svg>
                    Điểm thưởng
                    <span className="ml-1 text-yellow-600 font-bold">{loyaltyPoints}</span>
                    <span className="ml-1 text-xs text-gray-500">(≈ {Math.floor(loyaltyPoints/10).toLocaleString()}đ)</span>
                  </div>

                  {/* Popup hiển thị chi tiết điểm thưởng */}
                  {isShowLoyalty && (
                    <div
                      ref={loyaltyRef}
                      className="bg-white shadow-xl w-96 fixed top-[64px] rounded right-2 z-50"
                    >
                      <div className="p-4">
                        <div className="flex justify-between items-center border-b pb-3">
                          <h3 className="text-lg font-semibold text-gray-800">Điểm thưởng của bạn</h3>
                          <span className="text-2xl font-bold text-yellow-600">{loyaltyPoints} điểm</span>
                        </div>

                        <div className="mt-3">
                          <p className="text-sm text-gray-600 mb-2">Cách tích điểm:</p>
                          <ul className="text-xs text-gray-500 list-disc pl-5 mb-3">
                            <li>Mỗi 100,000đ = 1 điểm thưởng</li>
                            <li>Điểm thưởng có thể dùng để đổi quà hoặc tiền mặt</li>
                          </ul>

                          <p className="text-sm text-gray-600 mb-2">Quy đổi điểm thưởng:</p>
                          <ul className="text-xs text-gray-500 list-disc pl-5 mb-3">
                            <li>500 điểm = <span className="text-red-600 font-medium">50.000đ</span></li>
                            <li>1000 điểm = <span className="text-red-600 font-medium">100.000đ</span></li>
                            <li>2000 điểm = <span className="text-red-600 font-medium">200.000đ</span></li>
                            <li>5000 điểm = <span className="text-red-600 font-medium">Miễn phí vận chuyển</span> cho tất cả đơn hàng trong 3 tháng</li>
                          </ul>
                        </div>

                        <div className="mt-3">
                          <p className="text-sm text-gray-600 mb-2">Lịch sử tích điểm:</p>
                          <div className="max-h-40 overflow-y-auto">
                            <table className="w-full text-xs">
                              <thead className="bg-gray-100">
                                <tr>
                                  <th className="py-2 px-1 text-left">Ngày</th>
                                  <th className="py-2 px-1 text-right">Giá trị đơn hàng</th>
                                  <th className="py-2 px-1 text-right">Điểm nhận</th>
                                </tr>
                              </thead>
                              <tbody>
                                {purchaseHistory.map(item => (
                                  <tr key={item.id} className="border-b">
                                    <td className="py-2 px-1">{item.date}</td>
                                    <td className="py-2 px-1 text-right">{item.amount.toLocaleString('vi-VN')}đ</td>
                                    <td className="py-2 px-1 text-right text-yellow-600">+{item.points}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div className="mt-4 flex justify-between flex-wrap gap-2">
                          <button
                            onClick={() => setIsShowRedeemModal(true)}
                            className="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                          >
                            Đổi điểm thưởng
                          </button>
                          <button
                            onClick={refreshLoyaltyData}
                            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                          >
                            Làm mới
                          </button>
                          <button
                            onClick={() => {
                              // Test function để thêm điểm thưởng
                              const testPoints = 5;
                              addLocalPoints(testPoints);
                              const testHistory = {
                                id: Date.now(),
                                date: new Date().toLocaleDateString('vi-VN'),
                                amount: 500000,
                                points: testPoints
                              };
                              addLocalHistory(testHistory);
                              alert(`Đã thêm ${testPoints} điểm thưởng test!`);
                            }}
                            className="bg-green-500 hover:bg-green-600 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                          >
                            Test +5 điểm
                          </button>
                          <button
                            onClick={() => setIsShowHistoryModal(true)}
                            className="bg-white border border-yellow-500 text-yellow-600 hover:bg-yellow-50 py-2 px-3 rounded-lg text-xs font-medium transition-colors"
                          >
                            Lịch sử đổi điểm
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className=" relative xs:hidden lg:block">
                <a
                href="/gio-hang-san-pham"
                  className="flex mr-4 cursor-pointer  text-sm"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2.5}
                    stroke="currentColor"
                    className="size-5 mr-1"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
                    />
                  </svg>
                  Giỏ hàng
                </a>
                <p className=" left-2 absolute top-[-10px] bg-red-600  rounded-full text-center w-5 h-5 text-xs shadow-lg  text-white ">
                  {Cart.length}
                </p>
              </div>
              <div>
                <div
                  onClick={() => setIsShow(!isShow)}
                  className="flex items-center cursor-pointer"
                >
                  <img
                    className="pl-4 mr-2 h-[30px] w-auto object-cover"
                    src={avatar}
                    alt="Logo Bán Sách"
                  />
                  <span className=" xs:hidden lg:block text-sm">
                    {username}
                  </span>
                </div>
                {username !== "Tài khoản" ? (
                  <div
                    ref={menuRef}
                    className={` ${
                      isShow ? " block" : " hidden"
                    }  bg-white shadow-xl w-auto  fixed top-[64px] rounded right-2`}
                  >
                    <ul>
                      <li className="text-gray-500 px-6 py-2 cursor-pointer  hover:bg-gray-200 border-b border-solid border-gray-100 font-normal mt-2 text-sm">
                        <Link to={"/ho-so-ca-nhan"}>
                          Thông tin cá nhân
                        </Link>
                      </li>
                      <li className="text-gray-500 px-6 py-2 cursor-pointer hover:bg-gray-200 border-b border-solid border-gray-100 font-normal mt-2 text-sm">
                        <Link to={"/doi-mat-khau"} >Đổi mật khẩu</Link>
                      </li>
                      <li className="text-gray-500 px-6 py-2 cursor-pointer hover:bg-gray-200 border-b border-solid border-gray-100 font-normal mt-2 text-sm flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="size-4 mr-1 text-yellow-600"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"
                          />
                        </svg>
                        <span>Điểm thưởng: <span className="font-semibold text-yellow-600">{loyaltyPoints}</span> <span className="text-xs text-gray-500">(≈ {Math.floor(loyaltyPoints/10).toLocaleString()}đ)</span></span>
                      </li>
                      <li
                        onClick={handleLogout}
                        className="text-gray-500 px-6 py-2 cursor-pointer hover:bg-gray-200 border-b border-solid border-gray-100 font-normal mt-2 text-sm"
                      >
                        Đăng xuất
                      </li>
                    </ul>
                  </div>
                ) : (
                  <div
                    ref={menuRef}
                    className={` ${
                      isShow ? " block" : " hidden"
                    }   shadow-xl bg-white px-4 py-2 w-auto fixed top-[64px] rounded right-2`}
                  >
                    <ul>
                      <li className="text-white px-10 py-2 cursor-pointer bg-red-600 rounded-lg shadow-lg border-b border-solid font-semibold mt-2 text-sm">
                        <Link to={"/login"}>Đăng nhập</Link>
                      </li>
                      <li className="text-red-600 shadow-lg px-10 py-2 border-2 border-red-600  text-center  cursor-pointe rounded-lg font-semibold border-solid  mt-2 text-sm">
                        <Link to={"/register"}>Đăng ký</Link>
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Đổi điểm thưởng */}
      {isShowRedeemModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center border-b pb-3 mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Đổi điểm thưởng</h3>
              <button
                onClick={() => setIsShowRedeemModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Điểm thưởng hiện tại:</p>
              <p className="text-2xl font-bold text-yellow-600">{loyaltyPoints} điểm</p>
              <p className="text-xs text-gray-500">(≈ {Math.floor(loyaltyPoints/10).toLocaleString()}đ)</p>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-gray-700">Chọn phần thưởng:</h4>

              {/* Tiền mặt 50k */}
              <div
                onClick={() => handleRedeemPoints('50000')}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  loyaltyPoints >= 500
                    ? 'border-yellow-300 hover:bg-yellow-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-gray-800">50.000đ tiền mặt</p>
                    <p className="text-sm text-gray-500">Cần 500 điểm</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-red-600">50.000đ</p>
                    {loyaltyPoints < 500 && <p className="text-xs text-red-500">Không đủ điểm</p>}
                  </div>
                </div>
              </div>

              {/* Tiền mặt 100k */}
              <div
                onClick={() => handleRedeemPoints('100000')}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  loyaltyPoints >= 1000
                    ? 'border-yellow-300 hover:bg-yellow-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-gray-800">100.000đ tiền mặt</p>
                    <p className="text-sm text-gray-500">Cần 1000 điểm</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-red-600">100.000đ</p>
                    {loyaltyPoints < 1000 && <p className="text-xs text-red-500">Không đủ điểm</p>}
                  </div>
                </div>
              </div>

              {/* Tiền mặt 200k */}
              <div
                onClick={() => handleRedeemPoints('200000')}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  loyaltyPoints >= 2000
                    ? 'border-yellow-300 hover:bg-yellow-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-gray-800">200.000đ tiền mặt</p>
                    <p className="text-sm text-gray-500">Cần 2000 điểm</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-red-600">200.000đ</p>
                    {loyaltyPoints < 2000 && <p className="text-xs text-red-500">Không đủ điểm</p>}
                  </div>
                </div>
              </div>

              {/* Miễn phí vận chuyển */}
              <div
                onClick={() => handleRedeemPoints('shipping')}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  loyaltyPoints >= 5000
                    ? 'border-blue-300 hover:bg-blue-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-gray-800">Miễn phí vận chuyển</p>
                    <p className="text-sm text-gray-500">Cần 5000 điểm - Có hiệu lực 3 tháng</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-blue-600">FREE</p>
                    {loyaltyPoints < 5000 && <p className="text-xs text-red-500">Không đủ điểm</p>}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-4 border-t">
              <button
                onClick={() => setIsShowRedeemModal(false)}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Lịch sử đổi điểm */}
      {isShowHistoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center border-b pb-3 mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Lịch sử đổi điểm</h3>
              <button
                onClick={() => setIsShowHistoryModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {redeemHistory.length > 0 ? (
                redeemHistory.map((item) => (
                  <div key={item.id} className="border rounded-lg p-3 bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <p className="font-medium text-gray-800">{item.description}</p>
                        <p className="text-sm text-gray-500">{item.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-red-600">-{item.pointsUsed} điểm</p>
                        {item.redeemValue > 0 && (
                          <p className="text-xs text-green-600">+{item.redeemValue.toLocaleString('vi-VN')}đ</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Chưa có lịch sử đổi điểm</p>
                  <p className="text-sm text-gray-400 mt-2">Hãy tích lũy điểm thưởng và đổi quà nhé!</p>
                </div>
              )}
            </div>

            <div className="mt-6 pt-4 border-t">
              <button
                onClick={() => setIsShowHistoryModal(false)}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
