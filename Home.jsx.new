import React, { useEffect, useState } from "react";
import c1 from "../../image/course6.webp";
import c2 from "../../image/course5.webp";
import c3 from "../../image/course3.webp";
import c4 from "../../image/course4.webp";
import c5 from "../../image/sourse7.webp";

import banner1 from "../../image/banner1.webp";
import banner11 from "../../image/bn1.jpg";
import banner22 from "../../image/bn2.jpg";
import banner2 from "../../image/banner2.webp";
import { Link, useNavigate, useNavigation } from "react-router-dom";
import axios from "axios";
// ảnh sale off
// API
import { getListBooks } from "../../services/Listbook/Listbook";
import DiscountTag from "../../components/DiscountTag/DiscountTag";

import sale1 from "../../image/bcl1.jpg";
import sale2 from "../../image/bcl2.PNG";
import sale3 from "../../image/bcl3.jpg";
import sale4 from "../../image/bcl4.PNG";
