import React, { useEffect, useState, useRef } from "react";
import c1 from "../../image/course6.webp";
import c2 from "../../image/course5.webp";
import c3 from "../../image/course3.webp";
import c4 from "../../image/course4.webp";
import c5 from "../../image/sourse7.webp";

import banner1 from "../../image/banner1.webp";
import banner11 from "../../image/bn1.jpg";
import banner22 from "../../image/bn2.jpg";
import banner2 from "../../image/banner2.webp";
import { Link, useNavigate, useNavigation } from "react-router-dom";
import axios from "axios";
// ảnh sale off
// API
import { getListBooks } from "../../services/Listbook/Listbook";

import sale1 from "../../image/bcl1.jpg";
import sale2 from "../../image/bcl2.PNG";
import sale3 from "../../image/bcl3.jpg";
import sale4 from "../../image/bcl4.PNG";

// ảnh danh mục sản phẩm
import list1 from "../../image/g1.webp";
import list2 from "../../image/g2.webp";
import list3 from "../../image/g3.webp";
import list4 from "../../image/g4.webp";
import list5 from "../../image/g5.webp";
import list6 from "../../image/g6.webp";
import list7 from "../../image/list7.webp";
import list8 from "../../image/list8.webp";

const List = [
  {
    id: 1,
    title: "Guitar",
    image: list1,
    link: "danh-muc-guitar",
  },
  {
    id: 2,
    title: "Piano",
    image: list2,
    link: "danh-muc-piano",
  },
  {
    id: 3,
    title: "Trống",
    image: list3,
    link: "danh-muc-trong",
  },
  {
    id: 4,
    title: "Sáo",
    image: list4,
    link: "danh-muc-sao",
  },
  {
    id: 5,
    title: "Micro",
    image: list5,
    link: "danh-muc-micro",
  },
  {
    id: 6,
    title: "Organ",
    image: list6,
    link: "danh-muc-organ",
  },

];

const Home = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const totalSlides = 5;
  const [books, setBooks] = useState([]);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [isWednesday, setIsWednesday] = useState(false);
  const [isNineAM, setIsNineAM] = useState(false);
  const [showFlashSaleBanner, setShowFlashSaleBanner] = useState(false);

  // Chatbox states
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      text: "Xin chào! Tôi là trợ lý ảo của Music Store. Tôi có thể giúp gì cho bạn về thông tin cửa hàng, sản phẩm, địa chỉ hoặc khuyến mãi?",
      sender: "ai"
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);

  const navigate = useNavigate();

  // gọi API danh sách sản phẩm
  useEffect(() => {
    const fetchBooks = async () => {
      try {
        const response = await getListBooks();

        if (response.data.status === 1) {
          setBooks(response.data.books);
        } else {
          setError(response.data.msg);
        }
      } catch (error) {
        setError("Không thể tải danh sách sách. Vui lòng thử lại.");
      }
    };

    fetchBooks();

    // Check if today is Thursday
    const today = new Date();
    const isThurs = today.getDay() === 4; // 0 is Sunday, 4 is Thursday
    setIsWednesday(isThurs);

    // Check if current time is around 9 AM (between 8:30 AM and 9:30 AM)
    const currentHour = today.getHours();
    const currentMinute = today.getMinutes();
    const isNine = (currentHour === 8 && currentMinute >= 30) ||
                   (currentHour === 9 && currentMinute <= 30) ||
                   (currentHour === 9 && currentMinute === 0);
    setIsNineAM(isNine);

    // Show flash sale banner if it's Thursday or 9 AM
    setShowFlashSaleBanner(isThurs || isNine);

    // Set up interval to check time every minute
    const timeInterval = setInterval(() => {
      const now = new Date();
      const hour = now.getHours();
      const minute = now.getMinutes();
      const isNineNow = (hour === 8 && minute >= 30) ||
                        (hour === 9 && minute <= 30) ||
                        (hour === 9 && minute === 0);
      setIsNineAM(isNineNow);
      setShowFlashSaleBanner(isThurs || isNineNow);
    }, 60000); // Check every minute

    return () => clearInterval(timeInterval);
  }, []);

  useEffect(() => {
    // Cuộn lên đầu trang
    window.scrollTo(0, 0);
  }, []);

  // lấy id khi ấn vào sản phẩm và hiện ra trang chi tiết
  const handleDetail = (item) => {
    navigate(`/chi-tiet-san-pham/${item.id}`);
  };

  const changeSlide = (direction) => {
    setCurrentSlide((prev) => {
      const newIndex = prev + direction;
      if (newIndex < 0) return totalSlides - 1;
      if (newIndex >= totalSlides) return 0;
      return newIndex;
    });
  };
  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      changeSlide(1);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Scroll to bottom of chat messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (isChatOpen) {
      scrollToBottom();
    }
  }, [messages, isChatOpen]);

  // Handle sending a new message
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim() === "") return;

    // Add user message
    const userMessage = { text: newMessage, sender: "user" };
    setMessages([...messages, userMessage]);
    setNewMessage("");

    // Generate AI response based on user query
    setTimeout(() => {
      const aiResponse = generateAIResponse(newMessage);
      setMessages(prev => [...prev, { text: aiResponse, sender: "ai" }]);
    }, 500);
  };

  // Generate AI response based on user query
  const generateAIResponse = (query) => {
    const lowerQuery = query.toLowerCase();

    // Information about store locations
    if (lowerQuery.includes("địa chỉ") || lowerQuery.includes("cửa hàng") || lowerQuery.includes("showroom")) {
      return "Music Store có 2 showroom: \n1. 218 Lĩnh Nam, Hoàng Mai, Hà Nội \n2. Khu Đô Thị An Huy, Vũ Ninh, Bắc Ninh \nGiờ mở cửa: 8:30 - 20:00 tất cả các ngày trong tuần.";
    }

    // Information about promotions
    else if (lowerQuery.includes("khuyến mãi") || lowerQuery.includes("giảm giá") || lowerQuery.includes("ưu đãi")) {
      return "Music Store đang có chương trình khuyến mãi giảm giá 20% vào mỗi thứ Năm và giảm 5% vào lúc 9 giờ sáng mỗi ngày. Ngoài ra, chúng tôi còn có chương trình tích điểm thành viên để tri ân khách hàng.";
    }

    // Information about products
    else if (lowerQuery.includes("sản phẩm") || lowerQuery.includes("nhạc cụ") || lowerQuery.includes("đàn")) {
      return "Music Store chuyên kinh doanh các loại nhạc cụ như đàn guitar, piano, organ, trống, sáo, ukulele và các phụ kiện âm nhạc. Giá đàn guitar dao động từ 900.000đ đến hơn 5 triệu đồng tùy loại.";
    }

    // Information about contact
    else if (lowerQuery.includes("liên hệ") || lowerQuery.includes("số điện thoại") || lowerQuery.includes("hotline")) {
      return "Bạn có thể liên hệ với Music Store qua số điện thoại: 038886666 để được hỗ trợ.";
    }

    // Information about payment methods
    else if (lowerQuery.includes("thanh toán") || lowerQuery.includes("chuyển khoản") || lowerQuery.includes("qr")) {
      return "Music Store hỗ trợ thanh toán bằng tiền mặt, chuyển khoản ngân hàng (quét mã QR 'Music Store'), và các phương thức thanh toán trực tuyến phổ biến.";
    }

    // Default response
    else {
      return "Cảm ơn bạn đã liên hệ. Nếu bạn cần thông tin về địa chỉ cửa hàng, khuyến mãi, sản phẩm hoặc cách thức thanh toán, vui lòng cho tôi biết. Hoặc bạn có thể gọi số 038886666 để được hỗ trợ trực tiếp.";
    }
  };

  // Toggle chat window
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };



  return (
    <>
      <div className="">
        <div className="">
          {/* Flash Sale Banner */}
          {showFlashSaleBanner && (
            <div className="bg-gradient-to-r from-blue-600 to-red-600 text-white py-3 px-4 mx-14 mt-4 rounded-lg shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <div>
                    <h2 className="text-xl font-bold">
                      {isWednesday ? "FLASH SALE THỨ 5!" : "FLASH SALE 9H SÁNG!"}
                    </h2>
                    <p className="text-sm">
                      {isWednesday ? "Giảm giá 20% tất cả sản phẩm" : "Giảm giá 5% tất cả sản phẩm"}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm">Thời gian còn lại:</p>
                  <div className="text-xl font-bold">
                    {isWednesday ? "Hôm nay" : "Chỉ trong 1 giờ"}
                  </div>
                </div>
              </div>
            </div>
          )}

        <div className="mx-14 my-8">
          <div className="">
            <div className="grid grid-cols-5 grid-rows-2 gap-2 ">
              <div className="col-span-3 row-span-2 rounded  z-10 shadow-xl">
                <div className="relative w-full" id="carouselExampleIndicators">
                  <div className="relative overflow-hidden">
                    <div
                      className="flex transition-transform duration-500"
                      style={{
                        transform: `translateX(-${currentSlide * 100}%)`,
                      }}
                    >
                      <div className="carousel-item w-full flex-shrink-0 h-[300px]">
                        <img src={banner11} className="block w-full " alt="Course 1" />
                      </div>
                      <div className="carousel-item w-full flex-shrink-0 h-[300px]">
                        <img src={banner22} className="block w-full" alt="Course 2" />
                      </div>
                      <div className="carousel-item w-full flex-shrink-0 h-[300px]">
                        <img src={banner11} className="block w-full" alt="Course 3" />
                      </div>
                      <div className="carousel-item w-full flex-shrink-0 h-[300px]">
                        <img src={banner22} className="block w-full" alt="Course 4" />
                      </div>
                      <div className="carousel-item w-full flex-shrink-0 h-[300px]">
                        <img src={banner11} className="block w-full" alt="Course 5" />
                      </div>
                    </div>
                  </div>

                  <button
                    className="absolute flex items-center top-1/2 left-[-12px] z-10 p-2 w-8 h-8 bg-white cursor-pointer outline-none rounded-full"
                    onClick={() => changeSlide(-1)}
                  >
                    &#10094;
                  </button>

                  <button
                    className="absolute flex items-center top-1/2 right-[-12px] z-10 p-2 w-8 h-8 bg-white cursor-pointer outline-none rounded-full"
                    onClick={() => changeSlide(1)}
                  >
                    &#10095;
                  </button>

                  <div className="flex justify-center absolute top-[96%] left-[40%]">
                    {Array.from({ length: totalSlides }).map((_, index) => (
                      <button
                        key={index}
                        className={`w-3 h-3 mx-1 rounded-full ${
                          currentSlide === index ? "bg-red-500" : "bg-gray-300"
                        }`}
                        onClick={() => goToSlide(index)}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <div className="col-span-2 rounded shadow-lg row-span-1">
                <Link to="">
                  <img src={banner1} alt="banner1" />
                </Link>
              </div>
              <div className="col-span-2 rounded shadow-lg row-span-1">
                <Link to="">
                  <img src={banner2} alt="Banner2" />
                </Link>
              </div>
            </div>
            <div className=" grid grid-cols-4 mt-4 gap-4">
              <div className=" col-span-1 shadow-lg">
                <Link to="">
                  <img className="w-full h-36 object-cover  " src={sale1} alt="Ảnh khuyến mãi" />
                </Link>
              </div>
              <div className=" col-span-1 shadow-lg">
                <Link to="">
                  <img className="w-full h-36 object-cover  " src={sale2} alt="Ảnh khuyến mãi1" />
                </Link>
              </div>
              <div className=" col-span-1 shadow-lg">
                <Link to="">
                  <img className="w-full h-36 object-cover  " src={sale3} alt="Ảnh khuyến mãi2" />
                </Link>
              </div>
              <div className=" col-span-1 shadow-lg">
                <Link to="">
                  <img className="w-full h-36 object-cover  " src={sale4} alt="Ảnh khuyến mãi3" />
                </Link>
              </div>
            </div>
            <div className=" grid mt-4 shadow-lg rounded px-4 bg-white grid-cols-6 gap-4">
              <div className=" col-span-6">
                <div className=" ml-4 py-4 flex items-center justify-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="size-6 text-red-700"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                    />
                  </svg>
                  <p className=" ml-2 uppercase font-bold text-black ">
                    Danh mục sản phẩm
                  </p>
                </div>
                <div className=" border border-solid border-gray-100 mb-2"></div>
              </div>
              {List.map((item, index) => (
                <div key={index} className=" col-span-1  py-2">
                  <Link
                    className=" flex flex-col text-gray-500 hover:text-red-700 items-center justify-between"
                    to={item.link}
                  >
                    <img src={item.image} className=" h-20" alt={item.title} />
                    <p className="text-sm  text-center font-semibold  mt-3">
                      {item.title}
                    </p>
                  </Link>
                </div>
              ))}
            </div>
            <div className=" mt-2 grid grid-cols-5 gap-2">
              <div className=" col-span-5 bg-white shadow-lg rounded">
                <div className=" ml-4 py-4 flex items-center justify-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2.5}
                    stroke="currentColor"
                    className="size-6 text-red-700"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                    />
                  </svg>

                  <p className=" ml-2 uppercase font-bold text-black ">
                    Danh Sách sản phẩm
                  </p>
                </div>
                <div className=" border border-solid border-gray-100 mb-2"></div>
              </div>
              {books.slice(0,15).map((item) => (
                <div
                  onClick={() => handleDetail(item)}
                  key={item.id}
                  className=" col-span-1 cursor-pointer bg-white shadow-lg rounded transform transition-transform duration-300 hover:translate-y-1"
                >
                  <div className="">
                    <img
                      src={`/image/${item.image}`}
                      alt="Sản phẩm"
                      className=" h-[160px] ml-4"
                    />
                  </div>
                  <div className="mt-4 text-xs cursor-pointer hover:text-gray-600  font-normal text-gray-700">
                    <p className=" h-[40px] font-medium leading-[20px] mx-2  text-left line-clamp-2">
                      {item.title}
                    </p>
                    <div className="flex mt-3 items-center justify-start">
                      <p className=" text-gray-600  font-medium text-xs mx-2 line-through">
                        {" "}
                        {Number(item.priceold).toLocaleString("vi-VN")} đ
                      </p>
                      <p className=" bg-red-500 ml-2 text-sm text-white font-semibold shadow-lg rounded  px-2 py-1">
                        {" "}
                        - {item.sale}
                      </p>
                    </div>

                    {/* Flash Sale Badge */}
                    {(isWednesday || isNineAM) && (
                      <div className="mx-2 mt-2">
                        <div className="bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          {isWednesday ? "Flash Sale Thứ 5 - 20%" : "Flash Sale 9h - 5%"}
                        </div>
                      </div>
                    )}

                    <div className=" flex pb-3 mt-2 items-center justify-between mx-2">
                      <p className=" text-red-500 text-sm font-semibold ">
                        {Number(item.price).toLocaleString("vi-VN")} đ
                      </p>
                      <p className="text-gray-600 font-normal text-xs ">
                        {" "}
                        đã bán : {item.sold}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              <div className="col-span-5 rounded ">
                <div className=" flex items-center justify-center ">
                  <a href="/tat-ca-san-pham" className=" text-white  shadow-lg font-bold text-lg uppercase hover:bg-red-600 bg-red-700 px-8 py-2 rounded my-2">
                    Xem Thêm
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Chat Button */}
      <div
        className="fixed bottom-6 right-6 bg-red-600 text-white p-4 rounded-full shadow-lg cursor-pointer hover:bg-red-700 transition-all z-50"
        onClick={toggleChat}
      >
        {isChatOpen ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        )}
      </div>

      {/* Chat Window */}
      {isChatOpen && (
        <div className="fixed bottom-20 right-6 w-80 md:w-96 bg-white rounded-lg shadow-xl z-50 overflow-hidden flex flex-col" style={{ height: "500px", maxHeight: "70vh" }}>
          {/* Chat Header */}
          <div className="bg-red-600 text-white px-4 py-3 flex justify-between items-center">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
              <h3 className="font-medium">Trợ lý Music Store</h3>
            </div>
            <button onClick={toggleChat} className="text-white hover:text-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`mb-3 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}
              >
                <div
                  className={`inline-block px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-red-600 text-white'
                      : 'bg-white text-gray-800 border border-gray-200'
                  } max-w-[80%]`}
                >
                  <p className="whitespace-pre-line text-sm">{message.text}</p>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input */}
          <form onSubmit={handleSendMessage} className="border-t border-gray-200 p-3 flex">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Nhập tin nhắn..."
              className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="bg-red-600 text-white px-4 py-2 rounded-r-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </form>
        </div>
      )}
    </>
  );
};

export default Home;
